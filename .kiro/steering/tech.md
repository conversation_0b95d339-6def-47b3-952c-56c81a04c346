# 技术栈与构建系统

## 核心技术栈

- **前端框架**: Flutter (Dart) ^3.7.2
- **后端服务**: PocketBase
- **地图服务**: Flutter Map (基于 OpenStreetMap)
- **状态管理**: StatefulWidget
- **本地存储**: SharedPreferences
- **网络请求**: HTTP package
- **图片处理**: Image Picker
- **位置服务**: Geolocator

## 主要依赖库

```yaml
dependencies:
  flutter_map: ^8.1.1
  flutter_map_cancellable_tile_provider: ^3.1.0
  font_awesome_flutter: ^10.7.0
  shared_preferences: ^2.5.3
  uuid: ^4.5.1
  latlong2: ^0.9.1
  camera: ^0.11.1
  path_provider: ^2.1.2
  image_picker: ^1.0.7
  geolocator: ^14.0.0
  permission_handler: ^12.0.0+1
  pocketbase: ^0.23.0
  http: ^1.1.0
```

## 开发环境要求

- Flutter SDK >= 3.7.2
- Dart SDK >= 3.7.2
- PocketBase 服务器

## 常用命令

### Flutter 命令

```bash
# 获取依赖
flutter pub get

# 运行应用
flutter run

# 构建发布版本 (Android)
flutter build apk --release

# 构建发布版本 (iOS)
flutter build ios --release

# 清理构建缓存
flutter clean

# 升级 Flutter SDK
flutter upgrade

# 检查项目依赖
flutter pub outdated
```

### PocketBase 命令

```bash
# 启动 PocketBase 服务器
./pocketbase serve

# 指定端口启动
./pocketbase serve --http="0.0.0.0:8090"

# 指定数据目录
./pocketbase serve --dir="/path/to/data"
```

## 开发模式功能

在开发模式下，应用提供额外的测试和调试功能：
- PocketBase 测试页面 (`/dev/service-test`)
- 开发者工具菜单
- 详细的日志输出

## 代码风格与规范

- 使用 `flutter_lints` 包中推荐的 lint 规则
- 类名使用 PascalCase
- 变量和方法使用 camelCase
- 常量使用 SCREAMING_SNAKE_CASE
- 文件名使用 snake_case

## 测试策略

- 使用 `flutter_test` 包进行单元测试和 Widget 测试
- 测试文件放置在 `test/` 目录下
- 测试文件命名格式: `{feature}_test.dart`