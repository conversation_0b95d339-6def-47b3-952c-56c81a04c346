# 项目结构与组织

## 目录结构

```
lib/
├── config/          # 配置文件
│   ├── app_config.dart        # 应用配置
│   └── pocketbase_config.dart # PocketBase配置
│
├── models/          # 数据模型
│   ├── fishing_spot.dart      # 钓点模型
│   ├── message.dart           # 消息模型
│   ├── spot_comment.dart      # 评论模型
│   ├── spot_like.dart         # 点赞模型
│   ├── spot_photo.dart        # 照片模型
│   ├── user.dart              # 用户模型
│   ├── user_favorite.dart     # 收藏模型
│   └── user_follow.dart       # 关注模型
│
├── pages/           # 页面组件
│   ├── dev/                   # 开发测试页面
│   │   └── service_test_page.dart
│   ├── chat_page.dart         # 聊天页面
│   ├── home_page.dart         # 首页
│   ├── login_page.dart        # 登录页面
│   ├── main_screen.dart       # 主屏幕
│   ├── profile_page.dart      # 个人资料页面
│   ├── search_page.dart       # 搜索页面
│   └── splash_screen.dart     # 启动屏幕
│
├── services/        # 业务逻辑服务
│   ├── auth_service_new.dart       # 认证服务
│   ├── fishing_spot_service_new.dart # 钓点服务
│   ├── location_service.dart       # 位置服务
│   ├── service_locator.dart        # 服务定位器
│   ├── social_service.dart         # 社交服务
│   ├── tile_cache_service.dart     # 地图缓存服务
│   └── user_service_new.dart       # 用户服务
│
├── utils/           # 工具类
│   └── bubble_indicator_painter.dart # UI绘制工具
│
├── widgets/         # 可复用组件
│   ├── add_spot_bottom_sheet.dart  # 添加钓点底部表单
│   ├── dev_menu.dart               # 开发菜单
│   └── snackbar.dart               # 提示条
│
├── theme.dart       # 主题配置
└── main.dart        # 应用入口
```

## 架构模式

项目采用服务定位器模式进行依赖管理，主要分为以下几层：

1. **UI层**: 页面和组件
   - 负责用户界面展示和交互
   - 通过服务定位器访问服务层

2. **服务层**: 业务逻辑服务
   - 实现核心业务逻辑
   - 管理状态和数据流
   - 处理网络请求和本地存储

3. **数据层**: 模型和数据源
   - 定义数据结构
   - 处理数据序列化和反序列化

## 服务架构

服务之间的依赖关系如下：

```
UI层 (Pages & Widgets)
        ↓
ServiceLocator (服务定位器)
        ↓
┌─────────────────────────────────────┐
│            服务层 (Services)          │
├─────────────────────────────────────┤
│ AuthService        (认证服务)        │
│ UserService        (用户管理服务)     │
│ SocialService      (社交功能服务)     │
│ FishingSpotService (钓点管理服务)     │
│ LocationService    (位置服务)        │
│ TileCacheService   (缓存服务)        │
└─────────────────────────────────────┘
        ↓
┌─────────────────────────────────────┐
│            数据层 (Data)             │
├─────────────────────────────────────┤
│ PocketBase        (后端数据库)       │
│ SharedPreferences (本地存储)         │
└─────────────────────────────────────┘
```

## 数据库结构

PocketBase 数据库包含以下主要集合：

1. **users**: 用户信息
2. **fishing_spots**: 钓点信息
3. **spot_photos**: 钓点照片
4. **comments**: 评论
5. **spot_likes**: 点赞记录
6. **spot_favorites**: 收藏记录
7. **user_follows**: 用户关注关系
8. **messages**: 用户私信

## 资源组织

- **assets/images/**: 图片资源
- **assets/fonts/**: 字体资源

## 命名约定

- **文件命名**: 使用snake_case (如: fishing_spot.dart)
- **类命名**: 使用PascalCase (如: FishingSpot)
- **变量/方法**: 使用camelCase (如: getUserById)
- **常量**: 使用SCREAMING_SNAKE_CASE (如: MAX_SPOTS_PER_PAGE)

## 代码组织原则

1. **单一职责**: 每个类和服务只负责一个特定的业务领域
2. **依赖注入**: 通过ServiceLocator管理服务实例和依赖关系
3. **分层架构**: 清晰的UI层、服务层和数据层分离
4. **模块化**: 功能按模块组织，便于维护和扩展