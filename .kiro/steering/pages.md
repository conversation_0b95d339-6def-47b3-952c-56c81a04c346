# 应用页面结构与功能

## 主要页面

### 1. 启动页面 (SplashScreen)
**文件路径**: `lib/pages/splash_screen.dart`
**功能描述**:
- 应用启动时的加载页面
- 显示应用logo和加载动画
- 检查用户登录状态
- 根据登录状态导航到登录页面或主页面

### 2. 登录页面 (LoginPage)
**文件路径**: `lib/pages/login_page.dart`
**功能描述**:
- 用户登录和注册功能
- 支持邮箱密码登录
- 支持手机号登录
- 表单验证和错误提示
- 凭据保存功能
- 登录状态管理

### 3. 主屏幕 (MainScreen)
**文件路径**: `lib/pages/main_screen.dart`
**功能描述**:
- 应用的主要导航容器
- 底部导航栏，包含四个主要标签页
- 管理页面切换和状态
- 监听应用生命周期
- 处理返回键行为

### 4. 首页 (HomePage)
**文件路径**: `lib/pages/home_page.dart`
**功能描述**:
- 显示交互式地图界面
- 展示用户位置和附近钓点
- 支持地图类型切换（矢量图/卫星图）
- 支持注记层显示切换
- 长按添加新钓点功能
- 查看钓点详情
- 位置更新和跟踪

### 5. 搜索页面 (SearchPage)
**文件路径**: `lib/pages/search_page.dart`
**功能描述**:
- 搜索钓点和用户
- 筛选和排序功能
- 搜索历史记录
- 热门搜索推荐

### 6. 消息页面 (ChatPage)
**文件路径**: `lib/pages/chat_page.dart`
**功能描述**:
- 显示用户消息列表
- 支持私信功能
- 消息通知管理
- 未读消息提示

### 7. 个人资料页面 (ProfilePage)
**文件路径**: `lib/pages/profile_page.dart`
**功能描述**:
- 显示用户个人信息
- 编辑个人资料
- 查看发布的钓点
- 查看收藏的钓点
- 关注和粉丝管理
- 设置和退出登录

## 开发测试页面

### 服务测试页面 (ServiceTestPage)
**文件路径**: `lib/pages/dev/service_test_page.dart`
**功能描述**:
- 仅在开发模式下可用
- 测试各种服务功能
- PocketBase连接测试
- 位置服务测试
- 数据操作测试

## 页面组件关系

```
SplashScreen
    ├── 检查登录状态
    ├── 已登录 ──────────────────────┐
    └── 未登录 ──────> LoginPage ────┘
                                     │
                                     ▼
                                 MainScreen
                                     │
                    ┌───────┬───────┼───────┬───────┐
                    │       │       │       │       │
                    ▼       ▼       ▼       ▼       ▼
                HomePage SearchPage ChatPage ProfilePage
```

## 页面导航流程

1. 应用启动 → SplashScreen（初始化各种服务）
2. SplashScreen → MainScreen（无论用户是否登录）
3. MainScreen中根据登录状态显示不同的内容或功能
4. 未登录用户在需要时会被引导至LoginPage
5. MainScreen中通过底部导航栏切换四个主要标签页
6. 从HomePage可以查看钓点详情或添加新钓点
7. 从ProfilePage可以编辑个人资料或退出登录

## 页面状态管理

- 使用StatefulWidget管理页面状态
- 通过ServiceLocator访问服务
- 使用ValueNotifier监听状态变化
- 页面间通过Navigator传递参数

## 页面交互模式

- **地图交互**: 拖动、缩放、长按添加钓点
- **列表交互**: 上拉加载更多、下拉刷新
- **表单交互**: 输入验证、提交反馈
- **导航交互**: 底部标签页切换、页面跳转
- **弹窗交互**: 模态底部表单、对话框、提示条