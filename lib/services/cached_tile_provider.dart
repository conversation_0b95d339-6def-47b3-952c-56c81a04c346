import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:ui' as ui;
import 'tile_cache_service.dart';

/// 带缓存功能的瓦片提供者
/// 实现了完整的缓存逻辑：先检查缓存，缓存未命中时下载并缓存
class CachedTileProvider extends TileProvider {
  final TileCacheService _cacheService;
  final http.Client _httpClient;
  final Map<String, String> _headers;
  final Duration _timeout;

  CachedTileProvider({
    required TileCacheService cacheService,
    http.Client? httpClient,
    Map<String, String>? headers,
    Duration? timeout,
  })  : _cacheService = cacheService,
        _httpClient = httpClient ?? http.Client(),
        _headers = headers ?? {},
        _timeout = timeout ?? const Duration(seconds: 10);

  @override
  ImageProvider getImage(TileCoordinates coordinates, TileLayer options) {
    return CachedTileImageProvider(
      coordinates: coordinates,
      options: options,
      cacheService: _cacheService,
      httpClient: _httpClient,
      headers: _headers,
      timeout: _timeout,
    );
  }

  @override
  void dispose() {
    _httpClient.close();
    super.dispose();
  }
}

/// 缓存瓦片图片提供者
class CachedTileImageProvider extends ImageProvider<CachedTileImageProvider> {
  final TileCoordinates coordinates;
  final TileLayer options;
  final TileCacheService cacheService;
  final http.Client httpClient;
  final Map<String, String> headers;
  final Duration timeout;

  const CachedTileImageProvider({
    required this.coordinates,
    required this.options,
    required this.cacheService,
    required this.httpClient,
    required this.headers,
    required this.timeout,
  });

  @override
  Future<CachedTileImageProvider> obtainKey(ImageConfiguration configuration) {
    return SynchronousFuture<CachedTileImageProvider>(this);
  }

  @override
  ImageStreamCompleter loadImage(
    CachedTileImageProvider key,
    ImageDecoderCallback decode,
  ) {
    final chunkEvents = StreamController<ImageChunkEvent>();
    return MultiFrameImageStreamCompleter(
      codec: _loadAsync(key, chunkEvents, decode),
      chunkEvents: chunkEvents.stream,
      scale: 1.0,
      debugLabel: 'CachedTileImageProvider(${key.coordinates})',
      informationCollector: () => <DiagnosticsNode>[
        DiagnosticsProperty<CachedTileImageProvider>('Image provider', this),
        DiagnosticsProperty<TileCoordinates>('Coordinates', coordinates),
      ],
    );
  }

  Future<ui.Codec> _loadAsync(
    CachedTileImageProvider key,
    StreamController<ImageChunkEvent> chunkEvents,
    ImageDecoderCallback decode,
  ) async {
    try {
      assert(key == this);

      // 生成瓦片URL
      final url = _generateTileUrl();
      
      // 1. 首先尝试从缓存获取
      final cachedData = await cacheService.getCachedTile(
        options.urlTemplate!,
        coordinates.x,
        coordinates.y,
        coordinates.z,
      );

      Uint8List bytes;
      
      if (cachedData != null) {
        // 缓存命中
        bytes = cachedData;
        debugPrint('🟢 瓦片从缓存加载: ${coordinates.z}/${coordinates.x}/${coordinates.y}');
      } else {
        // 缓存未命中，从网络下载
        debugPrint('🔵 瓦片从网络下载: ${coordinates.z}/${coordinates.x}/${coordinates.y}');
        try {
          bytes = await _downloadTile(url, chunkEvents);
          
          // 下载成功后缓存
          await cacheService.cacheTile(
            options.urlTemplate!,
            coordinates.x,
            coordinates.y,
            coordinates.z,
            bytes,
          );
        } catch (downloadError) {
          // 记录下载错误
          cacheService.recordDownloadError();
          rethrow;
        }
      }

      // 解码图片
      return await decode(await ui.ImmutableBuffer.fromUint8List(bytes));
    } catch (e) {
      // 错误处理
      debugPrint('🔴 瓦片加载失败: ${coordinates.z}/${coordinates.x}/${coordinates.y} - $e');
      
      // 尝试生成错误占位图片
      try {
        final errorBytes = await _generateErrorTile();
        return await decode(await ui.ImmutableBuffer.fromUint8List(errorBytes));
      } catch (errorGenError) {
        debugPrint('🔴 生成错误占位图片失败: $errorGenError');
        rethrow;
      }
    } finally {
      await chunkEvents.close();
    }
  }

  /// 生成瓦片URL
  String _generateTileUrl() {
    var url = options.urlTemplate!;
    
    // 替换坐标占位符
    url = url.replaceAll('{x}', coordinates.x.toString());
    url = url.replaceAll('{y}', coordinates.y.toString());
    url = url.replaceAll('{z}', coordinates.z.toString());
    
    // 处理子域名
    if (options.subdomains.isNotEmpty) {
      final subdomain = options.subdomains[
        (coordinates.x + coordinates.y) % options.subdomains.length
      ];
      url = url.replaceAll('{s}', subdomain);
    }
    
    // 处理额外选项（如天地图的API密钥）
    if (options.additionalOptions.isNotEmpty) {
      for (final entry in options.additionalOptions.entries) {
        url = url.replaceAll('{${entry.key}}', entry.value.toString());
      }
    }
    
    return url;
  }

  /// 从网络下载瓦片
  Future<Uint8List> _downloadTile(
    String url,
    StreamController<ImageChunkEvent> chunkEvents,
  ) async {
    final request = http.Request('GET', Uri.parse(url));
    
    // 添加请求头
    request.headers.addAll(headers);
    
    // 添加用户代理
    request.headers['User-Agent'] = 'Flutter Map Tile Cache/1.0';

    final streamedResponse = await httpClient.send(request).timeout(timeout);
    
    if (streamedResponse.statusCode != 200) {
      throw HttpException(
        'HTTP ${streamedResponse.statusCode}: ${streamedResponse.reasonPhrase}',
        uri: Uri.parse(url),
      );
    }

    final contentLength = streamedResponse.contentLength;
    final bytes = <int>[];
    int downloadedBytes = 0;

    await for (final chunk in streamedResponse.stream) {
      bytes.addAll(chunk);
      downloadedBytes += chunk.length;
      
      // 发送下载进度事件
      if (contentLength != null) {
        chunkEvents.add(ImageChunkEvent(
          cumulativeBytesLoaded: downloadedBytes,
          expectedTotalBytes: contentLength,
        ));
      }
    }

    return Uint8List.fromList(bytes);
  }

  /// 生成错误占位瓦片
  Future<Uint8List> _generateErrorTile() async {
    // 创建一个简单的错误占位图片（256x256像素，灰色背景）
    const int size = 256;
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder);
    
    // 绘制灰色背景
    final paint = ui.Paint()..color = const ui.Color(0xFFE0E0E0);
    canvas.drawRect(ui.Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()), paint);
    
    // 绘制错误文本
    final textPainter = TextPainter(
      text: const TextSpan(
        text: '加载失败',
        style: TextStyle(
          color: ui.Color(0xFF757575),
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: ui.TextDirection.ltr,
    );
    
    textPainter.layout();
    final textOffset = ui.Offset(
      (size - textPainter.width) / 2,
      (size - textPainter.height) / 2,
    );
    textPainter.paint(canvas, textOffset);
    
    // 转换为图片数据
    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  @override
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) return false;
    return other is CachedTileImageProvider &&
        other.coordinates == coordinates &&
        other.options == options;
  }

  @override
  int get hashCode => Object.hash(coordinates, options);

  @override
  String toString() => 
      'CachedTileImageProvider(${coordinates.z}/${coordinates.x}/${coordinates.y})';
}

/// HTTP异常类
class HttpException implements Exception {
  final String message;
  final Uri? uri;

  const HttpException(this.message, {this.uri});

  @override
  String toString() => 'HttpException: $message${uri != null ? ' ($uri)' : ''}';
}