import 'package:flutter/foundation.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import 'cached_tile_provider.dart';

/// 瓦片缓存服务类
/// 为不同平台提供地图瓦片缓存功能
/// - 移动端：使用文件系统缓存
/// - Web端：使用SharedPreferences缓存（有限容量）
class TileCacheService {
  static const String _cacheKeyPrefix = 'tile_cache_';
  static const String _cacheMetaKey = 'tile_cache_meta';
  static const int _maxWebCacheEntries = 200; // Web端最大缓存条目数（降低以避免配额问题）
  static const Duration _cacheExpiry = Duration(days: 90); // 缓存过期时间：3个月

  // 单例模式
  static final TileCacheService _instance = TileCacheService._internal();
  factory TileCacheService() => _instance;
  TileCacheService._internal();

  // 缓存目录
  Directory? _cacheDirectory;

  // 缓存元数据
  Map<String, Map<String, dynamic>> _cacheMetadata = {};

  // 初始化状态
  bool _isInitialized = false;

  // 统计信息
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _downloadErrors = 0;

  /// 初始化缓存服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (!kIsWeb) {
        // 移动端：初始化文件系统缓存
        await _initializeFileCache();
      } else {
        // Web端：初始化SharedPreferences缓存
        await _initializeWebCache();
      }

      _isInitialized = true;
      debugPrint('瓦片缓存服务初始化完成');
    } catch (e) {
      debugPrint('瓦片缓存服务初始化失败: $e');
    }
  }

  /// 初始化文件系统缓存（移动端）
  Future<void> _initializeFileCache() async {
    final appDir = await getApplicationDocumentsDirectory();
    _cacheDirectory = Directory(path.join(appDir.path, 'tile_cache'));

    if (!await _cacheDirectory!.exists()) {
      await _cacheDirectory!.create(recursive: true);
    }

    // 加载缓存元数据
    await _loadCacheMetadata();

    // 清理过期缓存
    await _cleanExpiredCache();
  }

  /// 初始化Web缓存
  Future<void> _initializeWebCache() async {
    await _loadCacheMetadata();
    await _cleanExpiredCache();
  }

  /// 加载缓存元数据
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metaJson = prefs.getString(_cacheMetaKey);

      if (metaJson != null) {
        final decoded = jsonDecode(metaJson) as Map<String, dynamic>;
        _cacheMetadata = decoded.map(
          (key, value) => MapEntry(key, Map<String, dynamic>.from(value)),
        );
      }
    } catch (e) {
      debugPrint('加载缓存元数据失败: $e');
      _cacheMetadata = {};
    }
  }

  /// 保存缓存元数据
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metaJson = jsonEncode(_cacheMetadata);
      await prefs.setString(_cacheMetaKey, metaJson);
    } catch (e) {
      debugPrint('保存缓存元数据失败: $e');
    }
  }

  /// 清理过期缓存
  Future<void> _cleanExpiredCache() async {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheMetadata.entries) {
      final cachedTime = DateTime.fromMillisecondsSinceEpoch(
        entry.value['timestamp'] ?? 0,
      );

      if (now.difference(cachedTime) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      await _removeCacheEntry(key);
    }

    // Web端额外检查缓存条目数量限制
    if (kIsWeb && _cacheMetadata.length > _maxWebCacheEntries) {
      await _cleanOldestWebCacheEntries();
    }
    
    // Web端启动时主动清理，确保有足够空间
    if (kIsWeb && _cacheMetadata.length > _maxWebCacheEntries * 0.8) {
      await _cleanOldestWebCacheEntries();
    }

    if (expiredKeys.isNotEmpty) {
      await _saveCacheMetadata();
      debugPrint('清理了 ${expiredKeys.length} 个过期缓存条目');
    }
  }

  /// 清理最旧的Web缓存条目
  Future<void> _cleanOldestWebCacheEntries() async {
    final entries = _cacheMetadata.entries.toList();
    entries.sort(
      (a, b) =>
          (a.value['timestamp'] ?? 0).compareTo(b.value['timestamp'] ?? 0),
    );

    final toRemove = entries.length - _maxWebCacheEntries + 50; // 多清理50个
    for (int i = 0; i < toRemove && i < entries.length; i++) {
      await _removeCacheEntry(entries[i].key);
    }
  }

  /// 紧急清理缓存（当配额超出时）
  Future<void> _emergencyCleanCache() async {
    debugPrint('🚨 执行紧急缓存清理');
    
    final entries = _cacheMetadata.entries.toList();
    entries.sort(
      (a, b) =>
          (a.value['timestamp'] ?? 0).compareTo(b.value['timestamp'] ?? 0),
    );

    // 清理一半的缓存条目
    final toRemove = (entries.length * 0.5).ceil();
    for (int i = 0; i < toRemove && i < entries.length; i++) {
      await _removeCacheEntry(entries[i].key);
    }
    
    await _saveCacheMetadata();
    debugPrint('🚨 紧急清理完成，移除了 $toRemove 个缓存条目');
  }

  /// 移除缓存条目
  Future<void> _removeCacheEntry(String key) async {
    _cacheMetadata.remove(key);

    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_cacheKeyPrefix$key');
    } else if (_cacheDirectory != null) {
      final file = File(path.join(_cacheDirectory!.path, '$key.tile'));
      if (await file.exists()) {
        await file.delete();
      }
    }
  }

  /// 生成瓦片缓存键
  String _generateCacheKey(String urlTemplate, int x, int y, int z) {
    final url = urlTemplate
        .replaceAll('{x}', x.toString())
        .replaceAll('{y}', y.toString())
        .replaceAll('{z}', z.toString());
    return base64Encode(
      utf8.encode(url),
    ).replaceAll('/', '_').replaceAll('+', '-');
  }

  /// 获取缓存的瓦片
  Future<Uint8List?> getCachedTile(
    String urlTemplate,
    int x,
    int y,
    int z,
  ) async {
    if (!_isInitialized) return null;

    final cacheKey = _generateCacheKey(urlTemplate, x, y, z);

    // 检查缓存元数据
    if (!_cacheMetadata.containsKey(cacheKey)) {
      return null;
    }

    final metadata = _cacheMetadata[cacheKey]!;
    final cachedTime = DateTime.fromMillisecondsSinceEpoch(
      metadata['timestamp'],
    );

    // 检查是否过期
    if (DateTime.now().difference(cachedTime) > _cacheExpiry) {
      await _removeCacheEntry(cacheKey);
      return null;
    }

    try {
      Uint8List? result;
      if (kIsWeb) {
        result = await _getCachedTileWeb(cacheKey);
      } else {
        result = await _getCachedTileFile(cacheKey);
      }

      if (result != null) {
        _cacheHits++;
        debugPrint('🟢 瓦片从缓存加载: $z/$x/$y');
      } else {
        _cacheMisses++;
      }

      return result;
    } catch (e) {
      debugPrint('获取缓存瓦片失败: $e');
      return null;
    }
  }

  /// 从Web缓存获取瓦片
  Future<Uint8List?> _getCachedTileWeb(String cacheKey) async {
    final prefs = await SharedPreferences.getInstance();
    final base64Data = prefs.getString('$_cacheKeyPrefix$cacheKey');

    if (base64Data != null) {
      return base64Decode(base64Data);
    }

    return null;
  }

  /// 从文件缓存获取瓦片
  Future<Uint8List?> _getCachedTileFile(String cacheKey) async {
    if (_cacheDirectory == null) return null;

    final file = File(path.join(_cacheDirectory!.path, '$cacheKey.tile'));
    if (await file.exists()) {
      return await file.readAsBytes();
    }

    return null;
  }

  /// 缓存瓦片
  Future<void> cacheTile(
    String urlTemplate,
    int x,
    int y,
    int z,
    Uint8List data,
  ) async {
    if (!_isInitialized) return;

    final cacheKey = _generateCacheKey(urlTemplate, x, y, z);

    try {
      if (kIsWeb) {
        await _cacheTileWeb(cacheKey, data);
      } else {
        await _cacheTileFile(cacheKey, data);
      }

      // 更新元数据
      _cacheMetadata[cacheKey] = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'size': data.length,
      };

      await _saveCacheMetadata();
      debugPrint('🔵 瓦片已缓存: $z/$x/$y (${data.length} bytes)');
    } catch (e) {
      debugPrint('缓存瓦片失败: $e');
    }
  }

  /// 缓存瓦片到Web存储
  Future<void> _cacheTileWeb(String cacheKey, Uint8List data) async {
    // Web端限制单个瓦片大小
    if (data.length > 50 * 1024) return; // 降低到50KB限制

    try {
      final prefs = await SharedPreferences.getInstance();
      final base64Data = base64Encode(data);
      
      // 在存储前检查是否需要清理空间
      if (_cacheMetadata.length >= _maxWebCacheEntries) {
        await _cleanOldestWebCacheEntries();
      }
      
      await prefs.setString('$_cacheKeyPrefix$cacheKey', base64Data);
    } catch (e) {
      if (e.toString().contains('QuotaExceededError') || 
          e.toString().contains('exceeded the quota')) {
        debugPrint('🔴 存储配额超出，清理缓存后重试');
        
        // 清理一半的缓存
        await _emergencyCleanCache();
        
        // 重试一次
        try {
          final prefs = await SharedPreferences.getInstance();
          final base64Data = base64Encode(data);
          await prefs.setString('$_cacheKeyPrefix$cacheKey', base64Data);
        } catch (retryError) {
          debugPrint('🔴 重试缓存失败，跳过此瓦片: $retryError');
          return;
        }
      } else {
        debugPrint('🔴 Web缓存存储失败: $e');
        rethrow;
      }
    }
  }

  /// 缓存瓦片到文件
  Future<void> _cacheTileFile(String cacheKey, Uint8List data) async {
    if (_cacheDirectory == null) return;

    final file = File(path.join(_cacheDirectory!.path, '$cacheKey.tile'));
    await file.writeAsBytes(data);
  }

  /// 获取缓存统计信息
  Future<Map<String, dynamic>> getCacheStats() async {
    if (!_isInitialized) {
      return {'initialized': false};
    }

    int totalSize = 0;
    for (final metadata in _cacheMetadata.values) {
      totalSize += (metadata['size'] as int? ?? 0);
    }

    final totalRequests = _cacheHits + _cacheMisses;
    final hitRate = totalRequests > 0 ? (_cacheHits / totalRequests * 100) : 0.0;

    return {
      'initialized': true,
      'entryCount': _cacheMetadata.length,
      'totalSize': totalSize,
      'platform': kIsWeb ? 'web' : 'mobile',
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'downloadErrors': _downloadErrors,
      'hitRate': hitRate.toStringAsFixed(1),
      'totalRequests': totalRequests,
    };
  }

  /// 记录下载错误
  void recordDownloadError() {
    _downloadErrors++;
  }

  /// 重置统计信息
  void resetStats() {
    _cacheHits = 0;
    _cacheMisses = 0;
    _downloadErrors = 0;
    debugPrint('缓存统计信息已重置');
  }

  /// 清空所有缓存
  Future<void> clearCache() async {
    if (!_isInitialized) return;

    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        final keys = prefs.getKeys().where(
          (key) => key.startsWith(_cacheKeyPrefix),
        );
        for (final key in keys) {
          await prefs.remove(key);
        }
      } else if (_cacheDirectory != null) {
        if (await _cacheDirectory!.exists()) {
          await _cacheDirectory!.delete(recursive: true);
          await _cacheDirectory!.create(recursive: true);
        }
      }

      _cacheMetadata.clear();
      await _saveCacheMetadata();

      debugPrint('缓存已清空');
    } catch (e) {
      debugPrint('清空缓存失败: $e');
    }
  }

  /// 创建带缓存的瓦片提供者
  /// 返回自定义的缓存瓦片提供者，实现真正的缓存集成
  TileProvider createCachedTileProvider({
    http.Client? httpClient,
    Map<String, String>? headers,
    Duration? timeout,
  }) {
    if (!_isInitialized) {
      debugPrint('⚠️ 瓦片缓存服务未初始化，使用标准网络提供者');
      return NetworkTileProvider();
    }
    
    return CachedTileProvider(
      cacheService: this,
      httpClient: httpClient,
      headers: headers,
      timeout: timeout,
    );
  }

  /// 创建标准网络瓦片提供者（备用方案）
  TileProvider createNetworkTileProvider() {
    return NetworkTileProvider();
  }
}
