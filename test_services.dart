import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/service_locator.dart';
import 'package:fishing_app/services/auth_service_new.dart';
import 'package:fishing_app/services/user_service_new.dart';
import 'package:fishing_app/services/social_service.dart';
import 'package:fishing_app/services/fishing_spot_service_new.dart';
import 'package:fishing_app/config/pocketbase_config.dart';



/// 使用flutter test test_services.dart 命令运行测试

/// 服务功能测试脚本
void main() {
  group('服务架构测试', () {
    late ServiceLocator serviceLocator;

    setUpAll(() async {
      // 在测试环境中初始化PocketBase客户端
      try {
        await PocketBaseConfig.instance.initialize();
      } catch (e) {
        debugPrint('PocketBase初始化失败（测试环境正常）: $e');
      }

      serviceLocator = ServiceLocator.instance;
      await serviceLocator.registerServices();

      // 在测试环境中，某些服务可能无法完全初始化，这是正常的
      try {
        await serviceLocator.initializeServices();
      } catch (e) {
        debugPrint('服务初始化失败（测试环境正常）: $e');
      }
    });

    test('服务注册测试', () {
      expect(serviceLocator.isRegistered<AuthService>(), true);
      expect(serviceLocator.isRegistered<UserService>(), true);
      expect(serviceLocator.isRegistered<SocialService>(), true);
      expect(serviceLocator.isRegistered<FishingSpotService>(), true);
    });

    test('服务初始化测试', () {
      expect(serviceLocator.isInitialized<AuthService>(), true);
      expect(serviceLocator.isInitialized<UserService>(), true);
      expect(serviceLocator.isInitialized<SocialService>(), true);
      expect(serviceLocator.isInitialized<FishingSpotService>(), true);
    });

    test('服务访问测试', () {
      expect(Services.auth, isA<AuthService>());
      expect(Services.user, isA<UserService>());
      expect(Services.social, isA<SocialService>());
      expect(Services.fishingSpot, isA<FishingSpotService>());
    });

    test('认证服务功能测试', () {
      final authService = Services.auth;

      // 测试初始状态
      expect(authService.isLoggedIn, false);
      expect(authService.currentUser, null);

      // 测试权限检查
      expect(authService.requiresLogin('post_spot'), true);
      expect(authService.requiresLogin('view_spots'), false);
    });

    test('用户服务功能测试', () async {
      final userService = Services.user;

      // 测试初始状态
      expect(userService.isLoggedIn, false);
      expect(userService.currentUser, null);

      // 测试获取所有用户（应该返回空列表或缓存数据）
      final users = await userService.getAllUsers();
      expect(users, isA<List>());
    });

    test('社交服务功能测试', () {
      final socialService = Services.social;

      // 测试初始状态
      expect(socialService.currentUser, null);
    });

    test('钓点服务功能测试', () async {
      final fishingSpotService = Services.fishingSpot;

      // 测试初始状态
      expect(fishingSpotService.currentUser, null);

      // 测试获取所有钓点（应该返回空列表或缓存数据）
      final spots = await fishingSpotService.getAllSpots();
      expect(spots, isA<List>());
    });

    test('服务健康状态测试', () {
      final health = serviceLocator.getServiceHealth();

      expect(health, isA<Map<String, dynamic>>());
      expect(health.isNotEmpty, true);

      // 检查每个服务的健康状态
      for (final entry in health.entries) {
        final serviceHealth = entry.value as Map<String, dynamic>;
        expect(serviceHealth['registered'], true);
        expect(serviceHealth['initialized'], true);
        expect(serviceHealth['instance'], true);
      }
    });
  });

  group('服务集成测试', () {
    test('服务间依赖测试', () {
      // 测试认证服务独立性
      expect(Services.auth, isNotNull);

      // 测试用户服务依赖认证服务
      expect(Services.user.currentUser, Services.auth.currentUser);

      // 测试社交服务依赖认证服务
      expect(Services.social.currentUser, Services.auth.currentUser);

      // 测试钓点服务依赖认证服务
      expect(Services.fishingSpot.currentUser, Services.auth.currentUser);
    });

    test('服务状态同步测试', () {
      // 所有依赖认证的服务应该有相同的登录状态
      final authLoggedIn = Services.auth.isLoggedIn;
      final userLoggedIn = Services.user.isLoggedIn;

      expect(userLoggedIn, authLoggedIn);
    });
  });
}

/// 手动测试函数
void runManualTests() {
  debugPrint('=== 开始手动测试服务架构 ===');

  // 测试服务状态
  debugPrint('1. 服务注册状态:');
  final registeredServices = ServiceLocator.instance.getRegisteredServices();
  for (final service in registeredServices) {
    debugPrint('   - ${service.toString()}');
  }

  debugPrint('2. 服务初始化状态:');
  final initializedServices = ServiceLocator.instance.getInitializedServices();
  for (final service in initializedServices) {
    debugPrint('   - ${service.toString()}');
  }

  debugPrint('3. 服务健康检查:');
  final health = ServiceLocator.instance.getServiceHealth();
  health.forEach((serviceName, status) {
    final serviceStatus = status as Map<String, dynamic>;
    final isHealthy =
        serviceStatus['registered'] &&
        serviceStatus['initialized'] &&
        serviceStatus['instance'];
    debugPrint('   - $serviceName: ${isHealthy ? "✅ 健康" : "❌ 异常"}');
  });

  debugPrint('4. 服务访问测试:');
  try {
    final auth = Services.auth;
    debugPrint('   - AuthService: ✅ 可访问');
    debugPrint('     登录状态: ${auth.isLoggedIn}');
  } catch (e) {
    debugPrint('   - AuthService: ❌ 访问失败 - $e');
  }

  try {
    final user = Services.user;
    debugPrint('   - UserService: ✅ 可访问');
    debugPrint('     当前用户: ${user.currentUser?.nickname ?? "未登录"}');
  } catch (e) {
    debugPrint('   - UserService: ❌ 访问失败 - $e');
  }

  try {
    final social = Services.social;
    debugPrint('   - SocialService: ✅ 可访问');
  } catch (e) {
    debugPrint('   - SocialService: ❌ 访问失败 - $e');
  }

  try {
    final fishingSpot = Services.fishingSpot;
    debugPrint('   - FishingSpotService: ✅ 可访问');
  } catch (e) {
    debugPrint('   - FishingSpotService: ❌ 访问失败 - $e');
  }

  debugPrint('=== 手动测试完成 ===');
}
