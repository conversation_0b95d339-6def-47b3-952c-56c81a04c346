# Flutter钓鱼应用数据库架构说明

## 概述

本文档详细说明了Flutter钓鱼应用的PocketBase数据库架构设计，包括集合结构、关系、权限规则和性能优化。

## 数据库设计原则

### 核心设计理念
- **开放性**: 未登录用户可以读取钓点数据，促进内容发现
- **安全性**: 用户隐私数据受权限规则保护，防止未授权访问
- **性能**: 地理位置索引优化，支持高效的空间查询
- **扩展性**: 模块化设计，便于后续功能扩展
- **一致性**: 应用层数据同步，保证数据一致性

## 数据集合结构

### 1. users (用户集合)
**用途**: 存储用户基本信息和统计数据
**权限规则**: ✅ 启用

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 用户唯一标识 |
| username | TEXT | UNIQUE, NOT NULL | 用户名 |
| nickname | TEXT | NOT NULL | 昵称 |
| email | TEXT | UNIQUE, NOT NULL | 邮箱 |
| phone | TEXT | - | 手机号 |
| avatar_url | TEXT | - | 头像URL |
| bio | TEXT | - | 个人简介 |
| points | INTEGER | DEFAULT 0 | 积分 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 注册时间 |
| last_login_at | TIMESTAMPTZ | DEFAULT NOW() | 最后登录时间 |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | 更新时间 |

### 2. fishing_spots (钓点表)
**用途**: 存储钓点基本信息
**RLS策略**: ❌ 不启用（允许未登录用户读取）

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 钓点唯一标识 |
| user_id | UUID | FK→users.id, NOT NULL | 发布者ID |
| name | TEXT | NOT NULL | 钓点名称 |
| latitude | DOUBLE PRECISION | NOT NULL | 纬度 |
| longitude | DOUBLE PRECISION | NOT NULL | 经度 |
| description | TEXT | - | 描述信息 |
| likes_count | INTEGER | DEFAULT 0 | 点赞数统计 |
| unlikes_count | INTEGER | DEFAULT 0 | 不喜欢数统计 |
| comments_count | INTEGER | DEFAULT 0 | 评论数统计 |
| photos_count | INTEGER | DEFAULT 0 | 照片数统计 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | 更新时间 |

### 3. spot_photos (钓点照片表)
**用途**: 存储钓点照片信息
**RLS策略**: ❌ 不启用（跟随钓点权限）

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 照片唯一标识 |
| spot_id | UUID | FK→fishing_spots.id, NOT NULL | 钓点ID |
| user_id | UUID | FK→users.id, NOT NULL | 上传者ID |
| photo_url | TEXT | NOT NULL | 照片URL |
| is_panorama | BOOLEAN | DEFAULT FALSE | 是否为全景照片 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 上传时间 |

### 4. comments (评论表)
**用途**: 存储钓点评论
**RLS策略**: ❌ 不启用（跟随钓点权限）

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 评论唯一标识 |
| spot_id | UUID | FK→fishing_spots.id, NOT NULL | 钓点ID |
| user_id | UUID | FK→users.id, NOT NULL | 评论者ID |
| content | TEXT | NOT NULL | 评论内容 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 评论时间 |

### 5. spot_likes (钓点点赞表)
**用途**: 记录用户对钓点的点赞/不喜欢
**RLS策略**: ✅ 启用

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 记录唯一标识 |
| user_id | UUID | FK→users.id, NOT NULL | 用户ID |
| spot_id | UUID | FK→fishing_spots.id, NOT NULL | 钓点ID |
| is_like | BOOLEAN | NOT NULL | true=点赞，false=不喜欢 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 操作时间 |

**唯一约束**: (user_id, spot_id)

### 6. spot_favorites (钓点收藏表)
**用途**: 记录用户收藏的钓点
**RLS策略**: ✅ 启用

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 记录唯一标识 |
| user_id | UUID | FK→users.id, NOT NULL | 用户ID |
| spot_id | UUID | FK→fishing_spots.id, NOT NULL | 钓点ID |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 收藏时间 |

**唯一约束**: (user_id, spot_id)

### 7. user_follows (用户关注表)
**用途**: 记录用户间的关注关系
**RLS策略**: ✅ 启用

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 记录唯一标识 |
| follower_id | UUID | FK→users.id, NOT NULL | 关注者ID |
| following_id | UUID | FK→users.id, NOT NULL | 被关注者ID |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 关注时间 |

**唯一约束**: (follower_id, following_id)
**检查约束**: follower_id != following_id

### 8. messages (消息表)
**用途**: 存储用户间私信
**RLS策略**: ✅ 启用

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PK | 消息唯一标识 |
| sender_id | UUID | FK→users.id, NOT NULL | 发送者ID |
| receiver_id | UUID | FK→users.id, NOT NULL | 接收者ID |
| content | TEXT | NOT NULL | 消息内容 |
| is_read | BOOLEAN | DEFAULT FALSE | 是否已读 |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | 发送时间 |

## 关键功能

### 数据库函数

#### 1. get_spots_in_bounds()
获取指定地理范围内的钓点
```sql
SELECT * FROM get_spots_in_bounds(min_lat, min_lng, max_lat, max_lng, limit_count);
```

#### 2. get_nearby_spots()
获取用户附近的钓点（按距离排序）
```sql
SELECT * FROM get_nearby_spots(user_lat, user_lng, radius_km, limit_count);
```

#### 3. get_user_stats()
获取用户统计信息
```sql
SELECT * FROM get_user_stats(user_uuid);
```

#### 4. calculate_distance()
计算两点间距离（单位：公里）
```sql
SELECT calculate_distance(lat1, lng1, lat2, lng2);
```

### 自动触发器

- **统计计数器**: 自动更新likes_count, comments_count, photos_count
- **时间戳更新**: 自动更新updated_at字段
- **数据一致性**: 确保统计数据的准确性

### 存储桶配置

- **存储桶名称**: `fishing-photos`
- **公开访问**: ✅ 启用
- **上传权限**: 仅认证用户
- **删除权限**: 仅文件上传者

## 性能优化

### 索引策略
- 地理位置复合索引: `(latitude, longitude)`
- 时间索引: `created_at DESC`
- 外键索引: 所有外键字段
- 查询优化索引: `(user_id, spot_id)` 等

### 查询优化建议
1. 使用地理位置函数进行空间查询
2. 利用统计字段避免实时计算
3. 合理使用LIMIT限制结果集大小
4. 利用索引优化WHERE条件

## 安全策略

### RLS策略设计
- **开放数据**: 钓点、照片、评论对所有用户可见
- **私有数据**: 用户信息、收藏、关注关系受保护
- **操作权限**: 用户只能修改自己的数据

### 数据保护
- 用户隐私信息加密存储
- 敏感操作需要身份验证
- 防止SQL注入和XSS攻击
- 定期数据备份和恢复

## 使用建议

### 开发阶段
1. 使用Supabase测试页面验证功能
2. 监控查询性能和索引使用情况
3. 定期检查RLS策略的有效性

### 生产环境
1. 启用数据库连接池
2. 配置自动备份策略
3. 监控数据库性能指标
4. 定期执行数据清理任务

## 扩展计划

### 未来功能
- 钓点评分系统
- 鱼类识别和记录
- 天气数据集成
- 社区活动管理
- 钓鱼技巧分享

### 性能扩展
- 读写分离
- 数据分片
- 缓存层优化
- CDN加速
