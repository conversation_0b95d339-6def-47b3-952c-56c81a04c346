# QuotaExceededError 缓存问题修复报告

## 问题描述

用户遇到以下错误：
```
缓存瓦片失败: QuotaExceededError: Failed to execute 'setItem' on 'Storage': Setting the value of 'flutter.tile_cache_...' exceeded the quota.
```

## 问题原因分析

1. **浏览器存储限制**：Web端使用localStorage存储地图瓦片缓存，但localStorage有严格的容量限制（通常5-10MB）
2. **瓦片数据量大**：地图瓦片是图片数据，经过base64编码后占用空间更大
3. **缓存策略不当**：原有的缓存清理机制不够积极，容易导致存储空间耗尽
4. **错误处理不足**：没有针对配额超出的特殊处理机制

## 修复方案

### 1. 降低缓存限制
- 将Web端最大缓存条目数从1000降低到200
- 将单个瓦片大小限制从100KB降低到50KB

### 2. 增强配额管理
- 在存储前主动检查缓存条目数量
- 当接近限制时提前清理旧缓存
- 启动时主动清理，确保有足够空间

### 3. 添加错误处理机制
- 捕获QuotaExceededError异常
- 执行紧急缓存清理（清理一半缓存）
- 重试机制：清理后重新尝试存储

### 4. 优化清理策略
- 基于时间戳的LRU清理算法
- 紧急清理时一次性清理50%的缓存
- 常规清理时多清理50个条目，留出缓冲空间

## 具体修改

### 修改1：降低缓存限制
```dart
// 从1000降低到200
static const int _maxWebCacheEntries = 200; // Web端最大缓存条目数（降低以避免配额问题）
```

### 修改2：增强Web缓存存储逻辑
```dart
Future<void> _cacheTileWeb(String cacheKey, Uint8List data) async {
  // 降低单个瓦片大小限制到50KB
  if (data.length > 50 * 1024) return;

  try {
    // 存储前检查并清理
    if (_cacheMetadata.length >= _maxWebCacheEntries) {
      await _cleanOldestWebCacheEntries();
    }
    
    // 正常存储
    await prefs.setString('$_cacheKeyPrefix$cacheKey', base64Data);
  } catch (e) {
    if (e.toString().contains('QuotaExceededError')) {
      // 紧急清理并重试
      await _emergencyCleanCache();
      // 重试一次
      try {
        await prefs.setString('$_cacheKeyPrefix$cacheKey', base64Data);
      } catch (retryError) {
        // 重试失败，跳过此瓦片
        return;
      }
    }
  }
}
```

### 修改3：添加紧急清理方法
```dart
Future<void> _emergencyCleanCache() async {
  // 清理一半的缓存条目
  final toRemove = (entries.length * 0.5).ceil();
  for (int i = 0; i < toRemove && i < entries.length; i++) {
    await _removeCacheEntry(entries[i].key);
  }
}
```

### 修改4：启动时主动清理
```dart
// Web端启动时主动清理，确保有足够空间
if (kIsWeb && _cacheMetadata.length > _maxWebCacheEntries * 0.8) {
  await _cleanOldestWebCacheEntries();
}
```

## 修复效果

1. **防止配额超出**：通过降低缓存限制和主动清理，大大减少配额超出的可能性
2. **优雅降级**：即使发生配额超出，也能通过紧急清理和重试机制继续工作
3. **用户体验**：错误不再频繁出现，地图加载更加稳定
4. **性能优化**：更合理的缓存大小，减少浏览器存储压力

## 建议的进一步优化

1. **使用IndexedDB**：考虑替换localStorage为IndexedDB，获得更大的存储空间
2. **压缩算法**：对瓦片数据进行压缩，减少存储空间占用
3. **智能预测**：基于用户行为预测需要缓存的瓦片
4. **分级缓存**：重要瓦片优先保留，次要瓦片优先清理

## 验证方法

1. 在Web浏览器中打开应用
2. 大量浏览地图，触发瓦片下载和缓存
3. 观察控制台，确认不再出现QuotaExceededError
4. 使用开发者工具查看缓存统计，确认缓存条目数控制在200以内

## 总结

通过这次修复，我们成功解决了Web端地图瓦片缓存的配额超出问题。修复方案包括：
- ✅ 降低缓存限制，适应浏览器存储约束
- ✅ 增强错误处理，提供优雅降级机制
- ✅ 优化清理策略，确保存储空间充足
- ✅ 添加紧急清理，处理极端情况

这些改进确保了地图功能在Web端的稳定性和可靠性。