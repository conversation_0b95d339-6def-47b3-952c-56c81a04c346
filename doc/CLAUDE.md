# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

鱼窝子 (YuYiWo) is a Flutter-based fishing social application built with PocketBase backend. The app allows fishing enthusiasts to share fishing spots, photos, and interact with other anglers.

## Tech Stack

- **Frontend**: Flutter (Dart 3.7.2+)
- **Backend**: PocketBase
- **Map**: Flutter Map with OpenStreetMap
- **State Management**: StatefulWidget with Service Locator pattern
- **Local Storage**: SharedPreferences
- **Database**: PostgreSQL with PocketBase

## Quick Start Commands

### Setup & Installation
```bash
# Install dependencies
flutter pub get

# Configure PocketBase server
# Edit lib/config/app_config.dart and set pocketBaseUrl

# Run in debug mode
flutter run

# Run with specific device
flutter run -d <device_id>
```

### Development Commands
```bash
# Run linting
flutter analyze

# Run tests
flutter test

# Build debug APK
flutter build apk --debug

# Build release APK
flutter build apk --release

# Run integration tests
flutter test integration_test/

# Check outdated packages
flutter pub outdated
```

### Environment Configuration
```bash
# Set custom PocketBase URL via environment variable
flutter run --dart-define=POCKETBASE_URL=http://your-server:8090

# Enable/disable dev mode
flutter run --dart-define=FLUTTER_DEV_MODE=true
```

## Architecture Overview

### Service Architecture (Layered Design)
```
┌─────────────────┐
│     UI Layer    │ (Pages & Widgets)
├─────────────────┤
│  Service Layer  │ (Business Logic)
├─────────────────┤
│   Data Layer    │ (PocketBase + Local)
└─────────────────┘
```

### Core Services (via ServiceLocator)
- **AuthService**: User authentication, login/logout, session management
- **UserService**: User CRUD operations, profile management
- **SocialService**: Follow/unfollow, likes, comments, favorites, messaging
- **FishingSpotService**: Spot CRUD, search, photos, geographic queries
- **LocationService**: GPS, location permissions, geocoding
- **TileCacheService**: Map tile caching for offline use

### Model Structure
```
lib/models/
├── user.dart              # User model + PocketBase integration
├── fishing_spot.dart      # Fishing spot with geo-location
├── spot_comment.dart      # Comments on spots
├── spot_like.dart         # Like/dislike records
├── spot_photo.dart        # Photo attachments for spots
├── user_favorite.dart     # User's favorite spots
├── user_follow.dart       # User follow relationships
└── message.dart           # Direct messages between users
```

## Key Configuration Files

### App Configuration
- **Location**: `lib/config/app_config.dart`
- **Purpose**: Environment-specific settings (dev/prod)
- **Key settings**:
  - `pocketBaseUrl`: Server endpoint (default: http://*************:8090)
  - `isDevelopmentMode`: Enables debug features
  - `enableDeveloperTools`: Shows dev menu

### PocketBase Configuration
- **Location**: `lib/config/pocketbase_config.dart`
- **Purpose**: PocketBase client initialization
- **Features**: Error handling, offline mode fallback

## Development Workflow

### 1. Adding New Features
1. Create model in `lib/models/`
2. Add service method in appropriate service
3. Register service in `lib/services/service_locator.dart`
4. Create/update UI in `lib/pages/`
5. Add tests in `test/`

### 2. Database Changes
1. Update `DATABASE_ARCHITECTURE.md` with new schema
2. Create migration scripts if needed
3. Update corresponding model class
4. Test with existing data

### 3. Testing New Services
- Access `/dev/service-test` route (dev mode only)
- Use ServiceTestPage for manual testing
- Check service status with `serviceLocator.printServiceStatus()`

## Common Development Tasks

### Adding a New Service
```dart
// 1. Create service class
class NewService {
  final PocketBase pb;
  NewService(this.pb);
  
  // methods...
}

// 2. Register in ServiceLocator
Future<void> registerServices() async {
  _services['newService'] = NewService(_pb);
}

// 3. Add to Services helper
static NewService get newService => _instance._services['newService'] as NewService;
```

### Working with PocketBase
```dart
// Query records
final records = await pb.collection('fishing_spots').getFullList();

// Create record
final record = await pb.collection('fishing_spots').create(body: {...});

// Real-time subscriptions
pb.collection('fishing_spots').subscribe('*', (e) {
  // Handle updates
});
```

## Performance Considerations

### Caching Strategy
- User data: 5-minute cache
- Fishing spots: 5-minute cache
- Geographic queries: Separate cache per region

### Memory Management
- Dispose listeners in dispose() methods
- Use ValueListenableBuilder for reactive UI
- Clear caches on logout

## Testing Strategy

### Service Testing
- Unit tests: Test individual service methods
- Integration tests: Test service interactions
- Mock data: Use ServiceLocator for dependency injection

### UI Testing
- Widget tests: Test individual components
- Integration tests: Test full user flows
- Golden tests: Verify UI consistency

## Deployment Notes

### Build Configuration
- **Android**: Uses Gradle with Kotlin DSL
- **iOS**: Uses Swift with CocoaPods
- **Web**: Standard Flutter web build
- **Desktop**: Linux/Windows/macOS supported

### Environment Variables
- `POCKETBASE_URL`: Override server URL
- `FLUTTER_DEV_MODE`: Force dev/prod mode

### Release Checklist
1. Update app version in pubspec.yaml
2. Test on all target platforms
3. Update PocketBase server URL for production
4. Run full test suite
5. Build release artifacts
6. Test offline functionality