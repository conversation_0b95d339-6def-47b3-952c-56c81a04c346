# PocketBase 配置指南

## 概述

本文档说明如何为鱼窝子应用配置 PocketBase 后端服务。

## PocketBase 安装

### 1. 下载 PocketBase

访问 [PocketBase 官网](https://pocketbase.io/) 下载适合您系统的版本。

### 2. 启动 PocketBase

```bash
# 解压下载的文件
unzip pocketbase_xxx.zip

# 启动 PocketBase
./pocketbase serve
```

默认情况下，PocketBase 将在 `http://localhost:8090` 启动。

### 3. 访问管理界面

在浏览器中访问 `http://localhost:8090/_/` 进入管理界面，创建管理员账户。

## 数据集合配置

### 1. users 集合

创建用户集合，包含以下字段：

| 字段名 | 类型 | 必填 | 唯一 | 说明 |
|--------|------|------|------|------|
| username | text | ✅ | ✅ | 用户名 |
| email | email | ✅ | ✅ | 邮箱 |
| nickname | text | ✅ | ❌ | 昵称 |
| phone | text | ❌ | ❌ | 手机号 |
| avatar_url | url | ❌ | ❌ | 头像URL |
| bio | text | ❌ | ❌ | 个人简介 |
| following | json | ❌ | ❌ | 关注列表 |
| followers | json | ❌ | ❌ | 粉丝列表 |
| published_spots | json | ❌ | ❌ | 发布的钓点 |
| favorite_spots | json | ❌ | ❌ | 收藏的钓点 |

### 2. fishing_spots 集合

创建钓点集合，包含以下字段：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | text | ✅ | 钓点名称 |
| latitude | number | ✅ | 纬度 |
| longitude | number | ✅ | 经度 |
| description | text | ❌ | 描述 |
| shared_by | text | ✅ | 分享者 |
| user_id | relation | ✅ | 关联用户ID |
| photo_urls | json | ❌ | 照片URL列表 |
| panorama_photo_url | text | ❌ | 全景照片URL |
| likes_count | number | ❌ | 点赞数 |
| comments_count | number | ❌ | 评论数 |

### 3. spot_photos 集合

创建钓点照片集合：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| spot_id | relation | ✅ | 关联钓点ID |
| user_id | relation | ✅ | 上传用户ID |
| photo_url | url | ✅ | 照片URL |
| is_panorama | bool | ❌ | 是否为全景照片 |

### 4. comments 集合

创建评论集合：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| spot_id | relation | ✅ | 关联钓点ID |
| user_id | relation | ✅ | 评论用户ID |
| content | text | ✅ | 评论内容 |
| rating | number | ❌ | 评分 |

### 5. spot_likes 集合

创建点赞集合：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| spot_id | relation | ✅ | 关联钓点ID |
| user_id | relation | ✅ | 点赞用户ID |

## 权限规则配置

### users 集合权限

- **查看**: `@request.auth.id != "" || id = @request.auth.id`
- **创建**: `@request.auth.id != ""`
- **更新**: `id = @request.auth.id`
- **删除**: `id = @request.auth.id`

### fishing_spots 集合权限

- **查看**: `@request.auth.id != "" || @request.auth.id = ""`（公开可读）
- **创建**: `@request.auth.id != "" && user_id = @request.auth.id`
- **更新**: `@request.auth.id != "" && user_id = @request.auth.id`
- **删除**: `@request.auth.id != "" && user_id = @request.auth.id`

### 其他集合权限

类似配置，确保用户只能操作自己的数据。

## 文件存储配置

PocketBase 内置文件存储功能，支持：
- 图片上传和存储
- 自动生成缩略图
- 文件访问权限控制

## 应用配置

在 Flutter 应用中配置 PocketBase 服务器地址：

```dart
// lib/config/app_config.dart
String get pocketBaseUrl {
  return 'http://your-server:8090';
}
```

## 部署建议

### 生产环境部署

1. 使用反向代理（如 Nginx）
2. 配置 HTTPS 证书
3. 设置防火墙规则
4. 定期备份数据

### 环境变量

```bash
# 设置数据目录
export PB_DATA_DIR=/path/to/data

# 设置监听地址
export PB_BIND=0.0.0.0:8090
```

## 故障排除

### 常见问题

1. **连接失败**: 检查服务器地址和端口
2. **权限错误**: 检查集合权限规则配置
3. **文件上传失败**: 检查文件大小限制和权限

### 调试工具

- PocketBase 管理界面日志
- Flutter 应用开发者工具
- 网络请求监控

## 更多资源

- [PocketBase 官方文档](https://pocketbase.io/docs/)
- [PocketBase Dart SDK](https://github.com/pocketbase/dart-sdk)
- [Flutter 集成示例](https://github.com/pocketbase/dart-sdk/tree/master/example)
